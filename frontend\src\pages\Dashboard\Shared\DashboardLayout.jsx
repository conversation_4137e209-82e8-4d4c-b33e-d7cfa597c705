import { useState, useEffect } from 'react'
import { FiMenu, FiX } from 'react-icons/fi'
import { useBranding } from '../../../contexts/BrandingContext'
import DashboardSidebar from './DashboardSidebar'
import MobileMenu from './MobileMenu'

const DashboardLayout = ({ 
  children, 
  userType, 
  activeTab, 
  setActiveTab, 
  userData, 
  onLogout,
  sidebarItems 
}) => {
  const { branding } = useBranding()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Close mobile menu when tab changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [activeTab])

  // Close mobile menu on window resize to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div className="min-h-screen relative overflow-hidden"
         style={{
           background: `linear-gradient(135deg, ${branding.colors.primary}10, ${branding.colors.secondary}05, ${branding.colors.accent}08)`,
           backgroundSize: '400% 400%',
           animation: 'gradientShift 15s ease infinite'
         }}>
      
      {/* Mobile Header */}
      <div className="lg:hidden bg-white/90 backdrop-blur-sm border-b border-white/20 p-4 sticky top-0 z-40">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setIsMobileMenuOpen(true)}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <FiMenu className="w-6 h-6 text-gray-700" />
            </button>
            <div>
              <h1 className="text-lg font-bold text-gray-900">
                {userType === 'admin' ? 'Admin Dashboard' : 'My Dashboard'}
              </h1>
              <p className="text-sm text-gray-600">
                Welcome, {userData.name || userData.firstName || 'User'}
              </p>
            </div>
          </div>
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <span className="text-white font-semibold text-sm">
              {(userData.name || userData.firstName || 'U').charAt(0).toUpperCase()}
            </span>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        userType={userType}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        userData={userData}
        onLogout={onLogout}
        sidebarItems={sidebarItems}
      />

      <div className="flex">
        {/* Desktop Sidebar */}
        <div className="hidden lg:block">
          <DashboardSidebar
            userType={userType}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            userData={userData}
            onLogout={onLogout}
            sidebarItems={sidebarItems}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 lg:ml-80 min-h-screen">
          <div className="p-4 lg:p-8">
            {children}
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Global Styles */}
      <style jsx global>{`
        @keyframes gradientShift {
          0%, 100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }

        .line-clamp-1 {
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        /* Smooth transitions for all interactive elements */
        button, a, input, select, textarea {
          transition: all 0.2s ease-in-out;
        }

        /* Focus styles */
        button:focus-visible,
        a:focus-visible,
        input:focus-visible,
        select:focus-visible,
        textarea:focus-visible {
          outline: 2px solid ${branding.colors.primary};
          outline-offset: 2px;
        }

        /* Loading animation */
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.5;
          }
        }

        .animate-pulse {
          animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        /* Hover effects for cards */
        .hover-lift {
          transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }

        .hover-lift:hover {
          transform: translateY(-2px);
          box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Glass morphism effect */
        .glass {
          background: rgba(255, 255, 255, 0.8);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Gradient text */
        .gradient-text {
          background: linear-gradient(135deg, ${branding.colors.primary}, ${branding.colors.secondary});
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        /* Custom button styles */
        .btn-primary {
          background: linear-gradient(135deg, ${branding.colors.primary}, ${branding.colors.secondary});
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 0.75rem;
          font-weight: 600;
          transition: all 0.2s ease-in-out;
          cursor: pointer;
        }

        .btn-primary:hover {
          transform: translateY(-1px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .btn-secondary {
          background: linear-gradient(135deg, ${branding.colors.accent}, #f3d016);
          color: white;
          border: none;
          padding: 0.75rem 1.5rem;
          border-radius: 0.75rem;
          font-weight: 600;
          transition: all 0.2s ease-in-out;
          cursor: pointer;
        }

        .btn-secondary:hover {
          transform: translateY(-1px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        /* Status badges */
        .status-badge {
          padding: 0.25rem 0.75rem;
          border-radius: 9999px;
          font-size: 0.75rem;
          font-weight: 600;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }

        .status-pending {
          background-color: #fef3c7;
          color: #92400e;
        }

        .status-confirmed {
          background-color: #d1fae5;
          color: #065f46;
        }

        .status-completed {
          background-color: #dbeafe;
          color: #1e40af;
        }

        .status-cancelled {
          background-color: #fee2e2;
          color: #991b1b;
        }

        /* Table styles */
        .table-hover tbody tr:hover {
          background-color: #f9fafb;
        }

        /* Form styles */
        .form-input {
          width: 100%;
          padding: 0.75rem 1rem;
          border: 1px solid #d1d5db;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          transition: all 0.2s ease-in-out;
        }

        .form-input:focus {
          outline: none;
          border-color: ${branding.colors.primary};
          box-shadow: 0 0 0 3px ${branding.colors.primary}20;
        }

        /* Modal styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          backdrop-filter: blur(4px);
          z-index: 50;
        }

        .modal-content {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: white;
          border-radius: 1rem;
          box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
          max-height: 90vh;
          overflow-y: auto;
          z-index: 51;
        }

        /* Responsive utilities */
        @media (max-width: 640px) {
          .mobile-full {
            width: 100% !important;
          }
          
          .mobile-text-sm {
            font-size: 0.875rem !important;
          }
          
          .mobile-p-4 {
            padding: 1rem !important;
          }
        }
      `}</style>
    </div>
  )
}

export default DashboardLayout

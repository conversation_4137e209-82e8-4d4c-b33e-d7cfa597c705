import { useState, useEffect } from 'react'
import { FiMenu, FiX } from 'react-icons/fi'
import { useBranding } from '../../../contexts/BrandingContext'
import DashboardSidebar from './DashboardSidebar'
import MobileMenu from './MobileMenu'

const DashboardLayout = ({ 
  children, 
  userType, 
  activeTab, 
  setActiveTab, 
  userData, 
  onLogout,
  sidebarItems 
}) => {
  const { branding } = useBranding()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  // Close mobile menu when tab changes
  useEffect(() => {
    setIsMobileMenuOpen(false)
  }, [activeTab])

  // Close mobile menu on window resize to desktop
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setIsMobileMenuOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <div className="min-h-screen relative overflow-hidden"
         style={{
           background: `linear-gradient(135deg, ${branding.colors.primary}10, ${branding.colors.secondary}05, ${branding.colors.accent}08)`,
           backgroundSize: '400% 400%',
           animation: 'gradientShift 15s ease infinite'
         }}>
      
      {/* Mobile Header */}
      <div className="lg:hidden bg-white/90 backdrop-blur-sm border-b border-white/20 p-4 sticky top-0 z-40">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setIsMobileMenuOpen(true)}
              className="p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <FiMenu className="w-6 h-6 text-gray-700" />
            </button>
            <div>
              <h1 className="text-lg font-bold text-gray-900">
                {userType === 'admin' ? 'Admin Dashboard' : 'My Dashboard'}
              </h1>
              <p className="text-sm text-gray-600">
                Welcome, {userData.name || userData.firstName || 'User'}
              </p>
            </div>
          </div>
          <div className="w-8 h-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
            <span className="text-white font-semibold text-sm">
              {(userData.name || userData.firstName || 'U').charAt(0).toUpperCase()}
            </span>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <MobileMenu
        isOpen={isMobileMenuOpen}
        onClose={() => setIsMobileMenuOpen(false)}
        userType={userType}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        userData={userData}
        onLogout={onLogout}
        sidebarItems={sidebarItems}
      />

      <div className="flex">
        {/* Desktop Sidebar */}
        <div className="hidden lg:block">
          <DashboardSidebar
            userType={userType}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
            userData={userData}
            onLogout={onLogout}
            sidebarItems={sidebarItems}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1 lg:ml-80 min-h-screen">
          <div className="p-4 lg:p-8">
            {children}
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Global Styles */}
      <style jsx global>{`
        @keyframes gradientShift {
          0%, 100% {
            background-position: 0% 50%;
          }
          50% {
            background-position: 100% 50%;
          }
        }

        .line-clamp-1 {
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        .line-clamp-3 {
          display: -webkit-box;
          -webkit-line-clamp: 3;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        /* Custom scrollbar */
        .custom-scrollbar::-webkit-scrollbar {
          width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        /* Smooth transitions for all interactive elements */
        button, a, input, select, textarea {
          transition: all 0.2s ease-in-out;
        }

        /* Responsive utilities */
        @media (max-width: 640px) {
          .mobile-full {
            width: 100% !important;
          }

          .mobile-text-sm {
            font-size: 0.875rem !important;
          }

          .mobile-p-4 {
            padding: 1rem !important;
          }
        }
      `}</style>
    </div>
  )
}

export default DashboardLayout

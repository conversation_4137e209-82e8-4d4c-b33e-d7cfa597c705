import { useState, useEffect } from 'react'
import { FiUsers, FiDollarSign, FiCalendar, FiShoppingBag, FiUserCheck } from 'react-icons/fi'
import { useBranding } from '../../../contexts/BrandingContext'

const AdminOverview = ({ 
  dashboardStats, 
  appointments, 
  orders, 
  sectionLoading, 
  adminData 
}) => {
  const { branding } = useBranding()

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="relative overflow-hidden rounded-2xl p-8 shadow-lg border border-white/20"
           style={{
             background: `linear-gradient(135deg, ${branding.colors.primary}90, ${branding.colors.secondary}80, ${branding.colors.accent}70)`,
             backgroundSize: '400% 400%',
             animation: 'gradientShift 8s ease infinite'
           }}>
        <div className="absolute inset-0 bg-black/10"></div>
        <div className="relative z-10">
          <h2 className="text-3xl font-bold mb-2">Welcome back, {adminData.name}!</h2>
          <p className="text-white/80 text-lg">Here's your business overview for today</p>
        </div>
      </div>

      {/* Loading State for Dashboard Stats */}
      {sectionLoading.overview ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg animate-pulse">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-24"></div>
                  <div className="h-8 bg-gray-300 rounded w-16"></div>
                </div>
                <div className="w-12 h-12 bg-gray-300 rounded-xl"></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        /* Analytics Cards */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 font-medium">Total Customers</p>
                <p className="text-3xl font-bold text-gray-900">
                  {dashboardStats?.totalCustomers || 0}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  +{dashboardStats?.newCustomersThisMonth || 0} this month
                </p>
              </div>
              <div className="p-3 rounded-xl shadow-lg"
                   style={{ background: `linear-gradient(135deg, ${branding.colors.primary}20, ${branding.colors.secondary}20)` }}>
                <FiUsers className="w-6 h-6" style={{ color: branding.colors.secondary }} />
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 font-medium">Monthly Revenue</p>
                <p className="text-3xl font-bold text-gray-900">
                  ${dashboardStats?.monthlyRevenue?.toLocaleString() || '0'}
                </p>
                <p className="text-xs text-green-600 mt-1">
                  +{dashboardStats?.revenueGrowth || 0}% vs last month
                </p>
              </div>
              <div className="p-3 rounded-xl shadow-lg"
                   style={{ background: `linear-gradient(135deg, ${branding.colors.accent}20, #f3d01620)` }}>
                <FiDollarSign className="w-6 h-6" style={{ color: '#f3d016' }} />
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 font-medium">Today's Appointments</p>
                <p className="text-3xl font-bold text-gray-900">
                  {dashboardStats?.todaysAppointments || 0}
                </p>
                <p className="text-xs text-blue-600 mt-1">
                  {dashboardStats?.upcomingAppointments || 0} upcoming
                </p>
              </div>
              <div className="p-3 rounded-xl shadow-lg"
                   style={{ background: `linear-gradient(135deg, #8b5cf620, #a855f720)` }}>
                <FiCalendar className="w-6 h-6 text-purple-600" />
              </div>
            </div>
          </div>

          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 transform hover:scale-[1.02] transition-all duration-300 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 font-medium">Recent Orders</p>
                <p className="text-3xl font-bold text-gray-900">
                  {dashboardStats?.recentOrders || 0}
                </p>
                <p className="text-xs text-orange-600 mt-1">
                  {dashboardStats?.totalOrders || 0} total orders
                </p>
              </div>
              <div className="p-3 rounded-xl shadow-lg"
                   style={{ background: `linear-gradient(135deg, #ea580c20, #f97316 20)` }}>
                <FiShoppingBag className="w-6 h-6 text-orange-600" />
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Today's Appointments */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-bold text-gray-900">Today's Appointments</h3>
            <p className="text-gray-600 mt-1">Manage your schedule for today</p>
          </div>
          <button className="px-4 py-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]">
            View All
          </button>
        </div>

        {sectionLoading.appointments ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl animate-pulse">
                <div className="w-12 h-12 bg-gray-300 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-32"></div>
                  <div className="h-3 bg-gray-300 rounded w-24"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-16"></div>
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        ) : appointments.length > 0 ? (
          <div className="space-y-4">
            {appointments.slice(0, 3).map((appointment, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200 cursor-pointer">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 rounded-full flex items-center justify-center"
                       style={{ background: `linear-gradient(135deg, ${branding.colors.secondary}20, ${branding.colors.accent}20)` }}>
                    <FiUserCheck className="w-6 h-6" style={{ color: branding.colors.secondary }} />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">{appointment.customerName || appointment.name || 'Unknown Customer'}</p>
                    <p className="text-sm text-gray-600">{appointment.service || appointment.serviceType || 'Service'}</p>
                    <p className="text-xs text-gray-500">{appointment.email}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">
                    {appointment.time || 'Time not set'}
                  </p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    appointment.status === 'confirmed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {appointment.status || 'Pending'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiCalendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No appointments scheduled for today</p>
          </div>
        )}
      </div>

      {/* Recent Orders */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-xl font-bold text-gray-900">Recent Orders</h3>
            <p className="text-gray-600 mt-1">Latest product orders from customers</p>
          </div>
          <button className="px-4 py-2 bg-gradient-to-r from-green-600 to-green-700 text-white rounded-lg hover:from-green-700 hover:to-green-800 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]">
            View All
          </button>
        </div>

        {sectionLoading.orders ? (
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl animate-pulse">
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-32"></div>
                  <div className="h-3 bg-gray-300 rounded w-40"></div>
                </div>
                <div className="space-y-2">
                  <div className="h-4 bg-gray-300 rounded w-16"></div>
                  <div className="h-6 bg-gray-300 rounded w-20"></div>
                </div>
              </div>
            ))}
          </div>
        ) : orders.length > 0 ? (
          <div className="space-y-4">
            {orders.slice(0, 3).map((order, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gradient-to-r from-gray-50 to-white rounded-xl border border-gray-100 hover:shadow-md transition-all duration-200 cursor-pointer">
                <div>
                  <p className="font-semibold text-gray-900">Order #{order.id || `ORD-${index + 1}`}</p>
                  <p className="text-sm text-gray-600">{order.customer || 'Customer'}</p>
                  <p className="text-xs text-gray-500">
                    {order.createdAt ? new Date(order.createdAt).toLocaleDateString() : 'Date unknown'}
                  </p>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">${order.total?.toFixed(2) || '0.00'}</p>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${order.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'}`}>
                    {order.status || 'Pending'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8">
            <FiShoppingBag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No recent orders found</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default AdminOverview

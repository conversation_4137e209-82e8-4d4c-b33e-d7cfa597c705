import apiService from './api.js'

/**
 * Admin service for handling administrative functions
 */
class AdminService {
  /**
   * Get dashboard statistics
   */
  async getDashboardStats() {
    try {
      const response = await apiService.get('/admin/dashboard')
      return response
    } catch (error) {
      console.error('Get dashboard stats error:', error)
      throw error
    }
  }

  /**
   * Get analytics data
   */
  async getAnalytics(params = {}) {
    try {
      const response = await apiService.get('/admin/analytics', params)
      return response
    } catch (error) {
      console.error('Get analytics error:', error)
      throw error
    }
  }

  // Customer Management

  /**
   * Get all customers
   */
  async getCustomers(params = {}) {
    try {
      const response = await apiService.get('/admin/customers', params)
      return response
    } catch (error) {
      console.error('Get customers error:', error)
      throw error
    }
  }

  /**
   * Get customer details
   */
  async getCustomer(customerId) {
    try {
      const response = await apiService.get(`/admin/customers/${customerId}`)
      return response
    } catch (error) {
      console.error('Get customer error:', error)
      throw error
    }
  }

  /**
   * Get customer notes
   */
  async getCustomerNotes(customerId) {
    try {
      const response = await apiService.get(`/admin/customers/${customerId}/notes`)
      return response
    } catch (error) {
      console.error('Get customer notes error:', error)
      throw error
    }
  }

  /**
   * Add customer note
   */
  async addCustomerNote(customerId, noteData) {
    try {
      const response = await apiService.post(`/admin/customers/${customerId}/notes`, noteData)
      return response
    } catch (error) {
      console.error('Add customer note error:', error)
      throw error
    }
  }

  /**
   * Update customer note
   */
  async updateCustomerNote(noteId, noteData) {
    try {
      const response = await apiService.put(`/admin/customers/notes/${noteId}`, noteData)
      return response
    } catch (error) {
      console.error('Update customer note error:', error)
      throw error
    }
  }

  /**
   * Delete customer note
   */
  async deleteCustomerNote(noteId) {
    try {
      const response = await apiService.delete(`/admin/customers/notes/${noteId}`)
      return response
    } catch (error) {
      console.error('Delete customer note error:', error)
      throw error
    }
  }

  /**
   * Create customer
   */
  async createCustomer(customerData) {
    try {
      const response = await apiService.post('/admin/customers', customerData)
      return response
    } catch (error) {
      console.error('Create customer error:', error)
      throw error
    }
  }

  /**
   * Update customer
   */
  async updateCustomer(customerId, customerData) {
    try {
      const response = await apiService.put(`/admin/customers/${customerId}`, customerData)
      return response
    } catch (error) {
      console.error('Update customer error:', error)
      throw error
    }
  }

  /**
   * Delete customer
   */
  async deleteCustomer(customerId) {
    try {
      const response = await apiService.delete(`/admin/customers/${customerId}`)
      return response
    } catch (error) {
      console.error('Delete customer error:', error)
      throw error
    }
  }

  // Appointment Management

  /**
   * Get all appointments
   */
  async getAppointments(params = {}) {
    try {
      const response = await apiService.get('/admin/appointments', params)
      return response
    } catch (error) {
      console.error('Get appointments error:', error)
      throw error
    }
  }

  /**
   * Update appointment
   */
  async updateAppointment(appointmentId, appointmentData) {
    try {
      const response = await apiService.put(`/admin/appointments/${appointmentId}`, appointmentData)
      return response
    } catch (error) {
      console.error('Update appointment error:', error)
      throw error
    }
  }

  /**
   * Delete appointment
   */
  async deleteAppointment(appointmentId) {
    try {
      const response = await apiService.delete(`/admin/appointments/${appointmentId}`)
      return response
    } catch (error) {
      console.error('Delete appointment error:', error)
      throw error
    }
  }

  // Order Management

  /**
   * Get all orders
   */
  async getOrders(params = {}) {
    try {
      const response = await apiService.get('/admin/orders', params)
      return response
    } catch (error) {
      console.error('Get orders error:', error)
      throw error
    }
  }

  /**
   * Create appointment
   */
  async createAppointment(appointmentData) {
    try {
      const response = await apiService.post('/admin/appointments', appointmentData)
      return response
    } catch (error) {
      console.error('Create appointment error:', error)
      throw error
    }
  }

  /**
   * Update appointment
   */
  async updateAppointment(appointmentId, appointmentData) {
    try {
      const response = await apiService.put(`/admin/appointments/${appointmentId}`, appointmentData)
      return response
    } catch (error) {
      console.error('Update appointment error:', error)
      throw error
    }
  }

  /**
   * Delete appointment
   */
  async deleteAppointment(appointmentId) {
    try {
      const response = await apiService.delete(`/admin/appointments/${appointmentId}`)
      return response
    } catch (error) {
      console.error('Delete appointment error:', error)
      throw error
    }
  }

  /**
   * Update order
   */
  async updateOrder(orderId, orderData) {
    try {
      const response = await apiService.put(`/admin/orders/${orderId}`, orderData)
      return response
    } catch (error) {
      console.error('Update order error:', error)
      throw error
    }
  }

  /**
   * Delete order
   */
  async deleteOrder(orderId) {
    try {
      const response = await apiService.delete(`/admin/orders/${orderId}`)
      return response
    } catch (error) {
      console.error('Delete order error:', error)
      throw error
    }
  }

  // Product Management

  /**
   * Get all products
   */
  async getProducts(params = {}) {
    try {
      const response = await apiService.get('/admin/products', params)
      return response
    } catch (error) {
      console.error('Get products error:', error)
      throw error
    }
  }

  /**
   * Create product
   */
  async createProduct(productData) {
    try {
      const response = await apiService.post('/admin/products', productData)
      return response
    } catch (error) {
      console.error('Create product error:', error)
      throw error
    }
  }

  /**
   * Update product
   */
  async updateProduct(productId, productData) {
    try {
      const response = await apiService.put(`/admin/products/${productId}`, productData)
      return response
    } catch (error) {
      console.error('Update product error:', error)
      throw error
    }
  }

  /**
   * Delete product
   */
  async deleteProduct(productId) {
    try {
      const response = await apiService.delete(`/admin/products/${productId}`)
      return response
    } catch (error) {
      console.error('Delete product error:', error)
      throw error
    }
  }

  // User Role Management

  /**
   * Get all users with roles
   */
  async getUsers(params = {}) {
    try {
      const response = await apiService.get('/admin/users', params)
      return response
    } catch (error) {
      console.error('Get users error:', error)
      throw error
    }
  }

  /**
   * Update user role
   */
  async updateUserRole(userId, role) {
    try {
      const response = await apiService.put(`/admin/users/${userId}/role`, {
        role
      })
      return response
    } catch (error) {
      console.error('Update user role error:', error)
      throw error
    }
  }

  /**
   * Activate user
   */
  async activateUser(userId) {
    try {
      const response = await apiService.put(`/admin/users/${userId}/activate`)
      return response
    } catch (error) {
      console.error('Activate user error:', error)
      throw error
    }
  }

  /**
   * Deactivate user
   */
  async deactivateUser(userId) {
    try {
      const response = await apiService.put(`/admin/users/${userId}/deactivate`)
      return response
    } catch (error) {
      console.error('Deactivate user error:', error)
      throw error
    }
  }

  // Business Settings

  /**
   * Get business profile
   */
  async getBusinessProfile() {
    try {
      const response = await apiService.get('/admin/business-profile')
      return response
    } catch (error) {
      console.error('Get business profile error:', error)
      throw error
    }
  }

  /**
   * Update business profile
   */
  async updateBusinessProfile(profileData) {
    try {
      const response = await apiService.put('/admin/business-profile', profileData)
      return response
    } catch (error) {
      console.error('Update business profile error:', error)
      throw error
    }
  }

  /**
   * Get business hours
   */
  async getBusinessHours() {
    try {
      const response = await apiService.get('/business-hours')
      return response
    } catch (error) {
      console.error('Get business hours error:', error)
      throw error
    }
  }

  /**
   * Update business hours
   */
  async updateBusinessHours(hoursData) {
    try {
      const response = await apiService.put('/admin/business-hours', hoursData)
      return response
    } catch (error) {
      console.error('Update business hours error:', error)
      throw error
    }
  }

  /**
   * Get site settings
   */
  async getSiteSettings() {
    try {
      const response = await apiService.get('/admin/settings')
      return response
    } catch (error) {
      console.error('Get site settings error:', error)
      throw error
    }
  }

  /**
   * Update site settings
   */
  async updateSiteSettings(settingsData) {
    try {
      const response = await apiService.put('/admin/settings', settingsData)
      return response
    } catch (error) {
      console.error('Update site settings error:', error)
      throw error
    }
  }

  // Staff Management

  /**
   * Get all staff
   */
  async getStaff(params = {}) {
    try {
      const response = await apiService.get('/admin/staff', params)
      return response
    } catch (error) {
      console.error('Get staff error:', error)
      throw error
    }
  }

  /**
   * Create staff member
   */
  async createStaff(staffData) {
    try {
      const response = await apiService.post('/admin/staff', staffData)
      return response
    } catch (error) {
      console.error('Create staff error:', error)
      throw error
    }
  }

  /**
   * Update staff member
   */
  async updateStaff(staffId, staffData) {
    try {
      const response = await apiService.put(`/admin/staff/${staffId}`, staffData)
      return response
    } catch (error) {
      console.error('Update staff error:', error)
      throw error
    }
  }

  /**
   * Get staff availability
   */
  async getStaffAvailability(staffId) {
    try {
      const response = await apiService.get(`/admin/staff/${staffId}/availability`)
      return response
    } catch (error) {
      console.error('Get staff availability error:', error)
      throw error
    }
  }

  // Data Management

  /**
   * Export data
   */
  async exportData(type, params = {}) {
    try {
      const response = await apiService.get(`/admin/export/${type}`, params)
      return response
    } catch (error) {
      console.error('Export data error:', error)
      throw error
    }
  }

  /**
   * Import data
   */
  async importData(type, file) {
    try {
      const response = await apiService.upload(`/admin/import/${type}`, file)
      return response
    } catch (error) {
      console.error('Import data error:', error)
      throw error
    }
  }

  /**
   * Create backup
   */
  async createBackup() {
    try {
      const response = await apiService.post('/admin/backup')
      return response
    } catch (error) {
      console.error('Create backup error:', error)
      throw error
    }
  }

  /**
   * Restore backup
   */
  async restoreBackup(backupFile) {
    try {
      const response = await apiService.upload('/admin/restore', backupFile)
      return response
    } catch (error) {
      console.error('Restore backup error:', error)
      throw error
    }
  }

  /**
   * Clear cache
   */
  async clearCache() {
    try {
      const response = await apiService.post('/admin/cache/clear')
      return response
    } catch (error) {
      console.error('Clear cache error:', error)
      throw error
    }
  }

  // Branding Management

  /**
   * Get branding settings
   */
  async getBranding() {
    try {
      const response = await apiService.get('/admin/branding')
      return response
    } catch (error) {
      console.error('Get branding error:', error)
      throw error
    }
  }

  /**
   * Update branding settings
   */
  async updateBranding(brandingData) {
    try {
      const response = await apiService.put('/admin/branding', brandingData)
      return response
    } catch (error) {
      console.error('Update branding error:', error)
      throw error
    }
  }

  /**
   * Upload branding image
   */
  async uploadBrandingImage(file, type) {
    try {
      const response = await apiService.upload(`/admin/branding/upload/${type}`, file)
      return response
    } catch (error) {
      console.error('Upload branding image error:', error)
      throw error
    }
  }
}

// Create and export singleton instance
const adminService = new AdminService()
export default adminService

import { FiX, FiLogOut, FiUser, FiSettings } from 'react-icons/fi'
import { useBranding } from '../../../contexts/BrandingContext'

const MobileMenu = ({ 
  isOpen, 
  onClose, 
  userType, 
  activeTab, 
  setActiveTab, 
  userData, 
  onLogout,
  sidebarItems 
}) => {
  const { branding } = useBranding()

  if (!isOpen) return null

  const handleItemClick = (tabId) => {
    setActiveTab(tabId)
    onClose()
  }

  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      {/* Backdrop with blur */}
      <div 
        className="absolute inset-0 bg-black/30 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Menu Content */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90%] max-w-md">
        <div className="bg-white/95 backdrop-blur-lg rounded-2xl shadow-2xl border border-white/20 overflow-hidden">
          {/* Header */}
          <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  <span className="text-white font-bold text-lg">
                    {(userData?.name || userData?.firstName || 'U').charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <h2 className="text-lg font-bold text-gray-900">
                    {userData?.name || `${userData?.firstName || ''} ${userData?.lastName || ''}`.trim() || 'User'}
                  </h2>
                  <p className="text-sm text-gray-600">
                    {userType === 'admin' ? 'Administrator' : 'Customer'}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 rounded-full hover:bg-white/50 transition-colors duration-200"
              >
                <FiX className="w-6 h-6 text-gray-600" />
              </button>
            </div>
          </div>

          {/* Navigation */}
          <div className="max-h-96 overflow-y-auto custom-scrollbar p-4">
            <nav className="space-y-2">
              {sidebarItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleItemClick(item.id)}
                  className={`w-full flex items-center px-4 py-3 rounded-xl transition-all duration-200 group ${
                    activeTab === item.id
                      ? 'bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 border border-blue-200 shadow-sm'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  }`}
                >
                  <div className={`p-2 rounded-lg mr-3 transition-all duration-200 ${
                    activeTab === item.id
                      ? 'bg-blue-100 text-blue-600'
                      : 'bg-gray-100 text-gray-600 group-hover:bg-gray-200'
                  }`}>
                    <item.icon className="w-5 h-5" />
                  </div>
                  <div className="text-left flex-1">
                    <p className="font-medium">{item.label}</p>
                    {item.description && (
                      <p className="text-xs text-gray-500 mt-0.5">{item.description}</p>
                    )}
                  </div>
                  {item.badge && (
                    <span className="ml-2 px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
                      {item.badge}
                    </span>
                  )}
                </button>
              ))}
            </nav>
          </div>

          {/* Footer Actions */}
          <div className="p-4 border-t border-gray-200 space-y-2 bg-gray-50/50">
            <button
              onClick={() => handleItemClick('profile')}
              className={`w-full flex items-center px-4 py-3 rounded-xl transition-all duration-200 ${
                activeTab === 'profile'
                  ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border border-green-200'
                  : 'text-gray-700 hover:bg-white/50'
              }`}
            >
              <div className={`p-2 rounded-lg mr-3 ${
                activeTab === 'profile'
                  ? 'bg-green-100 text-green-600'
                  : 'bg-gray-100 text-gray-600'
              }`}>
                <FiUser className="w-5 h-5" />
              </div>
              <span className="font-medium">Profile</span>
            </button>

            {userType === 'admin' && (
              <button
                onClick={() => handleItemClick('settings')}
                className={`w-full flex items-center px-4 py-3 rounded-xl transition-all duration-200 ${
                  activeTab === 'settings'
                    ? 'bg-gradient-to-r from-purple-50 to-indigo-50 text-purple-700 border border-purple-200'
                    : 'text-gray-700 hover:bg-white/50'
                }`}
              >
                <div className={`p-2 rounded-lg mr-3 ${
                  activeTab === 'settings'
                    ? 'bg-purple-100 text-purple-600'
                    : 'bg-gray-100 text-gray-600'
                }`}>
                  <FiSettings className="w-5 h-5" />
                </div>
                <span className="font-medium">Settings</span>
              </button>
            )}

            <button
              onClick={() => {
                onLogout()
                onClose()
              }}
              className="w-full flex items-center px-4 py-3 rounded-xl text-red-600 hover:bg-red-50 transition-all duration-200 group"
            >
              <div className="p-2 rounded-lg mr-3 bg-red-100 text-red-600 group-hover:bg-red-200">
                <FiLogOut className="w-5 h-5" />
              </div>
              <span className="font-medium">Logout</span>
            </button>
          </div>

          {/* Branding Footer */}
          <div className="p-4 border-t border-gray-100 bg-gray-50/30">
            <div className="text-center">
              <p className="text-xs text-gray-500">
                Powered by {branding.companyName || 'Your Company'}
              </p>
              <p className="text-xs text-gray-400 mt-1">
                © 2024 All rights reserved
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default MobileMenu

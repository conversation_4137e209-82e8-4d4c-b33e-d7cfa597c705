import { useState, useEffect } from 'react'
import {
  FiEdit, FiImage, FiPackage, FiCalendar, FiShoppingBag, FiUsers,
  FiUserCheck, FiBarChart, FiSettings, FiHome, FiPlus, FiRefreshCw
} from 'react-icons/fi'
import BrandingModal from '../../../components/Modals/BrandingModal'
import { adminService } from '../../../services'

const AdminBranding = ({
  showToast,
  branding
}) => {
  const [brandingData, setBrandingData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [activeBrandingSection, setActiveBrandingSection] = useState('global')

  useEffect(() => {
    loadBrandingData()
  }, [])

  const loadBrandingData = async () => {
    try {
      setLoading(true)
      const response = await adminService.getBranding()
      setBrandingData(response.data || {})
    } catch (error) {
      console.error('Error loading branding data:', error)
      showToast('Error loading branding data', 'error')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveBranding = async (formData) => {
    try {
      await adminService.updateBranding(formData)
      setBrandingData(formData)
      showToast('Branding updated successfully!', 'success')
      // Refresh the page to apply new branding
      window.location.reload()
    } catch (error) {
      console.error('Error saving branding:', error)
      showToast('Error saving branding. Please try again.', 'error')
    }
  }

  const sections = [
    { id: 'global', name: 'Global/Site-wide', icon: FiSettings },
    { id: 'home', name: 'Home Page', icon: FiHome },
    { id: 'services', name: 'Services Page', icon: FiPackage },
    { id: 'shop', name: 'Shop Page', icon: FiShoppingBag },
    { id: 'consultation', name: 'Consultation Page', icon: FiCalendar },
    { id: 'login', name: 'Login Page', icon: FiUsers },
    { id: 'signup', name: 'Sign Up Page', icon: FiUserCheck },
    { id: 'cart', name: 'Cart Page', icon: FiShoppingBag },
    { id: 'productDetail', name: 'Product Detail', icon: FiPackage },
    { id: 'userDashboard', name: 'User Dashboard', icon: FiBarChart },
    { id: 'footer', name: 'Footer', icon: FiEdit }
  ]

  return (
    <div className="space-y-6">
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">Branding & Content Management</h2>
          <p className="text-gray-600 mt-1">
            Customize all text content, images, and branding elements across your website.
            Changes are automatically saved and will persist after page refresh.
          </p>
        </div>
        <div className="flex gap-3">
          <button
            onClick={loadBrandingData}
            disabled={loading}
            className="flex items-center px-4 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-all duration-200 cursor-pointer disabled:opacity-50"
          >
            <FiRefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          <button
            onClick={() => setShowModal(true)}
            className="flex items-center px-6 py-3 bg-gradient-to-r from-indigo-500 to-purple-500 text-white rounded-xl hover:from-indigo-600 hover:to-purple-600 transition-all duration-200 cursor-pointer shadow-lg hover:shadow-xl transform hover:scale-[1.02]"
          >
            <FiEdit className="w-4 h-4 mr-2" />
            Edit Branding
          </button>
        </div>
      </div>

      {/* Tips Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <FiEdit className="w-5 h-5 text-blue-600 mt-0.5" />
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-blue-800">Branding Management Tips</h3>
            <div className="mt-2 text-sm text-blue-700">
              <p className="mb-2">Use this section to customize all text content across your website:</p>
              <ul className="list-disc list-inside space-y-1 text-xs">
                <li><strong>Global:</strong> Site name, contact info, and social media links</li>
                <li><strong>Home:</strong> Hero section, features, and call-to-action content</li>
                <li><strong>Services:</strong> Service descriptions and benefits</li>
                <li><strong>Shop:</strong> Product page headers and newsletter content</li>
                <li><strong>Consultation:</strong> Form labels and information text</li>
                <li><strong>Login/Signup:</strong> Authentication page content</li>
                <li><strong>Cart:</strong> Shopping cart and checkout text</li>
                <li><strong>Product Detail:</strong> Product page labels and sections</li>
                <li><strong>User Dashboard:</strong> Dashboard labels and messages</li>
                <li><strong>Footer:</strong> Footer description and section titles</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Section Navigation */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        <div className="flex flex-wrap gap-2">
          {sections.map((section) => {
            const Icon = section.icon
            return (
              <button
                key={section.id}
                onClick={() => {
                  setActiveBrandingSection(section.id)
                  // Scroll to top when switching sections for better UX
                  window.scrollTo({ top: 0, behavior: 'smooth' })
                }}
                className={`flex items-center px-4 py-2 rounded-lg transition-colors duration-200 ${
                  activeBrandingSection === section.id
                    ? 'bg-yellow-50 text-yellow-700 border border-yellow-200'
                    : 'bg-gray-50 text-gray-700 hover:bg-gray-100 border border-transparent'
                }`}
              >
                <Icon className="w-4 h-4 mr-2" />
                {section.name}
              </button>
            )
          })}
        </div>
      </div>

      {/* Content Sections */}
      <div className="bg-white rounded-xl p-6 shadow-sm">
        {activeBrandingSection === 'global' && (
          <div className="space-y-6">
            {/* Site Information */}
            <div>
              <div className="flex items-center mb-4">
                <FiSettings className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Site Information</h3>
              </div>

              <div className="space-y-4">
                {renderInputField('Site Name', brandingContent.global.siteName, (value) => handleContentChange('global', 'siteName', value))}
                {renderInputField('Phone Number', brandingContent.global.phone, (value) => handleContentChange('global', 'phone', value), 'tel')}
                {renderInputField('Email Address', brandingContent.global.email, (value) => handleContentChange('global', 'email', value), 'email')}
                {renderInputField('Address', brandingContent.global.address, (value) => handleContentChange('global', 'address', value), 'text', '', 3)}
              </div>
            </div>

            {/* Social Media */}
            <div>
              <div className="flex items-center mb-4">
                <FiUsers className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Social Media</h3>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {renderInputField('Facebook', brandingContent.global.facebook, (value) => handleContentChange('global', 'facebook', value), 'text', 'Page Name')}
                {renderInputField('Instagram', brandingContent.global.instagram, (value) => handleContentChange('global', 'instagram', value), 'text', '@username')}
                {renderInputField('Twitter', brandingContent.global.twitter, (value) => handleContentChange('global', 'twitter', value), 'text', '@username')}
                {renderInputField('YouTube', brandingContent.global.youtube, (value) => handleContentChange('global', 'youtube', value), 'text', 'Channel Name')}
              </div>
            </div>
          </div>
        )}

        {activeBrandingSection === 'home' && (
          <div className="space-y-6">
            {/* Hero Section */}
            <div>
              <div className="flex items-center mb-4">
                <FiImage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Hero Section</h3>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-4">
                  {renderInputField('Hero Title (Part 1)', brandingContent.home.heroTitleStart, (value) => handleContentChange('home', 'heroTitleStart', value))}
                  {renderInputField('Hero Title (Part 2)', brandingContent.home.heroTitleMiddle, (value) => handleContentChange('home', 'heroTitleMiddle', value))}
                  {renderInputField('Hero Title (Part 3)', brandingContent.home.heroTitleEnd, (value) => handleContentChange('home', 'heroTitleEnd', value))}
                  {renderInputField('Hero Subtitle', brandingContent.home.heroSubtitle, (value) => handleContentChange('home', 'heroSubtitle', value), 'text', '', 4)}
                </div>

                <div className="space-y-4">
                  {renderInputField('Hero Image URL', brandingContent.home.heroImage, (value) => handleContentChange('home', 'heroImage', value), 'url')}
                  {renderInputField('Hero Video URL', brandingContent.home.heroVideo, (value) => handleContentChange('home', 'heroVideo', value), 'url')}
                  {renderInputField('CTA Button Text', brandingContent.home.ctaButtonText, (value) => handleContentChange('home', 'ctaButtonText', value))}
                  
                  {brandingContent.home.heroImage && (
                    <div className="mt-2">
                      <img 
                        src={brandingContent.home.heroImage} 
                        alt="Hero preview"
                        className="w-full h-32 object-cover rounded-lg"
                      />
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Features Section */}
            <div>
              <div className="flex items-center mb-4">
                <FiPackage className="w-5 h-5 mr-2" style={{ color: '#f3d016' }} />
                <h3 className="text-lg font-semibold">Features Section</h3>
              </div>

              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {renderInputField('Features Title', brandingContent.home.featuresTitle, (value) => handleContentChange('home', 'featuresTitle', value))}
                  {renderInputField('Features Subtitle', brandingContent.home.featuresSubtitle, (value) => handleContentChange('home', 'featuresSubtitle', value))}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Feature 1</h4>
                    {renderInputField('Title', brandingContent.home.feature1Title, (value) => handleNestedContentChange('home', 'feature1Title', value))}
                    {renderInputField('Description', brandingContent.home.feature1Description, (value) => handleNestedContentChange('home', 'feature1Description', value), 'text', '', 3)}
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Feature 2</h4>
                    {renderInputField('Title', brandingContent.home.feature2Title, (value) => handleNestedContentChange('home', 'feature2Title', value))}
                    {renderInputField('Description', brandingContent.home.feature2Description, (value) => handleNestedContentChange('home', 'feature2Description', value), 'text', '', 3)}
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Feature 3</h4>
                    {renderInputField('Title', brandingContent.home.feature3Title, (value) => handleNestedContentChange('home', 'feature3Title', value))}
                    {renderInputField('Description', brandingContent.home.feature3Description, (value) => handleNestedContentChange('home', 'feature3Description', value), 'text', '', 3)}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Add more sections as needed */}
        {activeBrandingSection !== 'global' && activeBrandingSection !== 'home' && (
          <div className="text-center py-12">
            <FiEdit className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {sections.find(s => s.id === activeBrandingSection)?.name} Content
            </h3>
            <p className="text-gray-600">
              Content management for this section will be implemented here.
            </p>
          </div>
        )}
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <FiRefreshCw className="w-6 h-6 animate-spin text-indigo-500" />
            <span className="text-gray-600">Loading branding data...</span>
          </div>
        </div>
      )}

      {/* Branding Modal */}
      <BrandingModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        onSave={handleSaveBranding}
        brandingData={brandingData}
        isEdit={!!brandingData}
      />
    </div>
  )
}

export default AdminBranding

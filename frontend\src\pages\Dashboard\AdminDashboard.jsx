import { useState, useEffect, useCallback, useRef } from 'react'
import {
  FiUsers,
  FiCalendar,
  FiShoppingBag,
  FiSettings,
  FiBarChart,
  FiPackage,
  FiEdit
} from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'
import { useRouter } from '../../hooks/useRouter'
import { useAdminDataLoader } from '../../hooks/useLazyDataLoader'
import { adminService } from '../../services'
import Loading from '../../components/Loading'

// Import new component structure
import DashboardLayout from './Shared/DashboardLayout'
import AdminOverview from './Admin/AdminOverview'
import AdminAppointments from './Admin/AdminAppointments'
import AdminCustomers from './Admin/AdminCustomers'
import AdminOrders from './Admin/AdminOrders'
import AdminProducts from './Admin/AdminProducts'
import AdminBranding from './Admin/AdminBranding'
import AdminSettings from './Admin/AdminSettings'

const AdminDashboard = ({ onNavigate, onLogout, userProfile }) => {
  const { branding } = useBranding()
  const { parseCurrentRoute, navigateToSubRoute } = useRouter()
  const dataLoader = useAdminDataLoader()

  // Check if user has admin role
  useEffect(() => {
    if (userProfile && userProfile.role !== 'admin') {
      console.log('User does not have admin role, redirecting to user dashboard')
      onNavigate('user-dashboard')
      return
    }
  }, [userProfile, onNavigate])

  // Initialize activeTab from URL if available
  const [activeTab, setActiveTab] = useState(() => {
    const { mainRoute, subRoute } = parseCurrentRoute()
    return (mainRoute === 'admin-dashboard' && subRoute) ? subRoute : 'overview'
  })

  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [viewingItem, setViewingItem] = useState(null)
  const [toast, setToast] = useState(null)
  const [confirmDialog, setConfirmDialog] = useState(null)
  const [modalType, setModalType] = useState('') // 'add', 'edit', 'view'

  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const loadingRef = useRef(false)














  // Load data based on active tab using lazy loading
  const loadDataForTab = useCallback(async (tabId) => {
    try {
      await dataLoader.loadDataForTab(tabId, adminService)
    } catch (error) {
      console.error(`Error loading data for ${tabId}:`, error)
      setError(`Failed to load ${tabId} data`)
    }
  }, [dataLoader.loadDataForTab])

  // Update URL when tab changes and load required data
  const handleTabChange = (tabId) => {
    setActiveTab(tabId)
    navigateToSubRoute('admin-dashboard', tabId, { scrollToTop: true })

    // Clear cached data for the specific tab to ensure fresh data
    const dataKeysToRefresh = {
      'overview': ['dashboardStats', 'appointments', 'customers', 'orders', 'products'],
      'appointments': ['appointments'],
      'customers': ['customers'],
      'orders': ['orders'],
      'products': ['products']
    }

    const keysToRefresh = dataKeysToRefresh[tabId] || []
    keysToRefresh.forEach(key => dataLoader.clearData(key))

    loadDataForTab(tabId)
  }

  // Listen to route changes (browser back/forward) and update activeTab
  useEffect(() => {
    const { subRoute } = parseCurrentRoute()
    if (subRoute && subRoute !== activeTab) {
      setActiveTab(subRoute)
      loadDataForTab(subRoute)
    }
  }, [parseCurrentRoute, activeTab, loadDataForTab])

  // Load initial data on component mount
  useEffect(() => {
    const initializeDashboard = async () => {
      setIsLoading(true)
      setError('')

      try {
        // Set initial URL if no sub-route is present
        const { mainRoute, subRoute } = parseCurrentRoute()
        if (mainRoute === 'admin-dashboard' && !subRoute) {
          navigateToSubRoute('admin-dashboard', 'overview', { replace: true, scrollToTop: false })
          // Load data for overview tab
          loadDataForTab('overview')
        } else if (subRoute) {
          // Load data for the current tab
          loadDataForTab(subRoute)
        }
      } catch (error) {
        console.error('Error initializing dashboard:', error)
        setError('Failed to load dashboard. Please try again.')
      } finally {
        setIsLoading(false)
      }
    }

    initializeDashboard()
    // Only run once on mount
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  // Toast notification helper
  const showToast = (message, type = 'success') => {
    setToast({ message, type })
    setTimeout(() => setToast(null), 3000)
  }

  // Confirmation dialog helper
  const showConfirmDialog = (title, message, onConfirm) => {
    setConfirmDialog({ title, message, onConfirm })
  }

  // CRUD Handlers for different entities
  const handleDeleteAppointment = async (appointmentId) => {
    try {
      await adminService.deleteAppointment(appointmentId)
      showToast('Appointment deleted successfully!', 'success')
      // Clear cached data and reload
      dataLoader.clearData('appointments')
      loadDataForTab('appointments')
    } catch (error) {
      console.error('Delete appointment error:', error)
      showToast('Failed to delete appointment. Please try again.', 'error')
    }
  }

  const handleDeleteCustomer = async (customerId) => {
    try {
      await adminService.deleteCustomer(customerId)
      showToast('Customer deleted successfully!', 'success')
      // Clear cached data and reload
      dataLoader.clearData('customers')
      loadDataForTab('customers')
    } catch (error) {
      console.error('Delete customer error:', error)
      showToast('Failed to delete customer. Please try again.', 'error')
    }
  }

  const handleDeleteOrder = async (orderId) => {
    try {
      await adminService.deleteOrder(orderId)
      showToast('Order deleted successfully!', 'success')
      // Clear cached data and reload
      dataLoader.clearData('orders')
      loadDataForTab('orders')
    } catch (error) {
      console.error('Delete order error:', error)
      showToast('Failed to delete order. Please try again.', 'error')
    }
  }

  const handleDeleteProduct = async (productId) => {
    try {
      await adminService.deleteProduct(productId)
      showToast('Product deleted successfully!', 'success')
      // Clear cached data and reload
      dataLoader.clearData('products')
      loadDataForTab('products')
    } catch (error) {
      console.error('Delete product error:', error)
      showToast('Failed to delete product. Please try again.', 'error')
    }
  }

  if (isLoading) {
    return <Loading />
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Dashboard</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FiBarChart },
    { id: 'appointments', label: 'Appointments', icon: FiCalendar },
    { id: 'customers', label: 'Customers', icon: FiUsers },
    { id: 'orders', label: 'Orders', icon: FiShoppingBag },
    { id: 'products', label: 'Products', icon: FiPackage },
    { id: 'branding', label: 'Branding', icon: FiEdit },
    { id: 'settings', label: 'Settings', icon: FiSettings }
  ]

  // Render the appropriate component based on activeTab
  const renderActiveComponent = () => {
    // Function to refresh data for current tab
    const refreshCurrentTabData = () => {
      dataLoader.clearData(activeTab)
      loadDataForTab(activeTab)
    }

    const commonProps = {
      searchTerm,
      setSearchTerm,
      statusFilter,
      setStatusFilter,
      showAddModal,
      setShowAddModal,
      editingItem,
      setEditingItem,
      viewingItem,
      setViewingItem,
      modalType,
      setModalType,
      showToast,
      showConfirmDialog,
      sectionLoading: dataLoader.loading,
      branding,
      adminData: userProfile,
      handleDeleteAppointment,
      handleDeleteCustomer,
      handleDeleteOrder,
      handleDeleteProduct,
      refreshData: refreshCurrentTabData
    }

    switch (activeTab) {
      case 'overview':
        return (
          <AdminOverview
            {...commonProps}
            dashboardStats={dataLoader.getData('dashboardStats', {})}
            appointments={(() => {
              const data = dataLoader.getData('appointments', [])
              const array = Array.isArray(data) ? data : (data?.data && Array.isArray(data.data)) ? data.data : []
              return array.slice(0, 5)
            })()}
            customers={(() => {
              const data = dataLoader.getData('customers', [])
              const array = Array.isArray(data) ? data : (data?.data && Array.isArray(data.data)) ? data.data : []
              return array.slice(0, 5)
            })()}
            orders={(() => {
              const data = dataLoader.getData('orders', [])
              const array = Array.isArray(data) ? data : (data?.data && Array.isArray(data.data)) ? data.data : []
              return array.slice(0, 5)
            })()}
            products={(() => {
              const data = dataLoader.getData('products', [])
              const array = Array.isArray(data) ? data : (data?.data && Array.isArray(data.data)) ? data.data : []
              return array.slice(0, 5)
            })()}
            onNavigateToTab={handleTabChange}
          />
        )
      case 'appointments':
        return (
          <AdminAppointments
            {...commonProps}
            appointments={dataLoader.getData('appointments', [])}
          />
        )
      case 'customers':
        return (
          <AdminCustomers
            {...commonProps}
            customers={dataLoader.getData('customers', [])}
          />
        )
      case 'orders':
        return (
          <AdminOrders
            {...commonProps}
            orders={dataLoader.getData('orders', [])}
          />
        )
      case 'products':
        return (
          <AdminProducts
            {...commonProps}
            products={dataLoader.getData('products', [])}
          />
        )
      case 'branding':
        return (
          <AdminBranding
            showToast={showToast}
            branding={branding}
          />
        )
      case 'settings':
        return (
          <AdminSettings
            {...commonProps}
          />
        )
      default:
        return (
          <AdminOverview
            {...commonProps}
            dashboardStats={dataLoader.getData('dashboardStats', {})}
            appointments={(() => {
              const data = dataLoader.getData('appointments', [])
              const array = Array.isArray(data) ? data : (data?.data && Array.isArray(data.data)) ? data.data : []
              return array.slice(0, 5)
            })()}
            customers={(() => {
              const data = dataLoader.getData('customers', [])
              const array = Array.isArray(data) ? data : (data?.data && Array.isArray(data.data)) ? data.data : []
              return array.slice(0, 5)
            })()}
            orders={(() => {
              const data = dataLoader.getData('orders', [])
              const array = Array.isArray(data) ? data : (data?.data && Array.isArray(data.data)) ? data.data : []
              return array.slice(0, 5)
            })()}
            products={(() => {
              const data = dataLoader.getData('products', [])
              const array = Array.isArray(data) ? data : (data?.data && Array.isArray(data.data)) ? data.data : []
              return array.slice(0, 5)
            })()}
            onNavigateToTab={handleTabChange}
          />
        )
    }
  }

  return (
    <DashboardLayout
      userType="admin"
      activeTab={activeTab}
      setActiveTab={handleTabChange}
      userData={userProfile}
      onLogout={onLogout}
      sidebarItems={tabs}
    >
      {renderActiveComponent()}
    </DashboardLayout>
  )
}

export default AdminDashboard

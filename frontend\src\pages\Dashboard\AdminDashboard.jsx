import { useState, useEffect, useCallback, useRef } from 'react'
import {
  FiUsers,
  FiCalendar,
  FiShoppingBag,
  FiSettings,
  FiBarChart,
  FiPackage,
  FiEdit
} from 'react-icons/fi'
import { useBranding } from '../../contexts/BrandingContext'
import { useRouter } from '../../hooks/useRouter'
import { adminService } from '../../services'
import Loading from '../../components/Loading'

// Import new component structure
import DashboardLayout from './Shared/DashboardLayout'
import AdminOverview from './Admin/AdminOverview'
import AdminAppointments from './Admin/AdminAppointments'
import AdminCustomers from './Admin/AdminCustomers'
import AdminOrders from './Admin/AdminOrders'
import AdminProducts from './Admin/AdminProducts'
import AdminBranding from './Admin/AdminBranding'
import AdminSettings from './Admin/AdminSettings'

const AdminDashboard = ({ onNavigate, onLogout, userProfile }) => {
  const { branding } = useBranding()
  const { parseCurrentRoute, navigateToSubRoute } = useRouter()

  // Check if user has admin role
  useEffect(() => {
    if (userProfile && userProfile.role !== 'admin') {
      console.log('User does not have admin role, redirecting to user dashboard')
      onNavigate('user-dashboard')
      return
    }
  }, [userProfile, onNavigate])

  // Initialize activeTab from URL if available
  const [activeTab, setActiveTab] = useState(() => {
    const { mainRoute, subRoute } = parseCurrentRoute()
    return (mainRoute === 'admin-dashboard' && subRoute) ? subRoute : 'overview'
  })

  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [viewingItem, setViewingItem] = useState(null)
  const [toast, setToast] = useState(null)
  const [confirmDialog, setConfirmDialog] = useState(null)
  const [modalType, setModalType] = useState('') // 'add', 'edit', 'view'
  const [activeBrandingSection, setActiveBrandingSection] = useState('global')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const [sectionLoading, setSectionLoading] = useState({
    dashboard: false,
    appointments: false,
    customers: false,
    orders: false,
    products: false
  })
  const loadingRef = useRef(false)

  // Data state
  const [dashboardStats, setDashboardStats] = useState(null)
  const [appointments, setAppointments] = useState([])
  const [customers, setCustomers] = useState([])
  const [orders, setOrders] = useState([])
  const [products, setProducts] = useState([])


  // Load branding content from localStorage or use defaults
  const getInitialBrandingContent = () => {
    const saved = localStorage.getItem('goldielocs_branding_content')
    if (saved) {
      try {
        return JSON.parse(saved)
      } catch (error) {
        console.error('Error parsing saved branding content:', error)
      }
    }
    return {
      // Site-wide/Global content
      global: {
        siteName: 'Goldie Locs',
        tagline: 'By Tina',
        logo: 'https://via.placeholder.com/150x50/f3d016/000000?text=Goldie+Locs',
        phone: '(*************',
        email: '<EMAIL>',
        address: '123 Beauty Street, Atlanta, GA 30309',
        instagram: '@goldielocsbytina',
        facebook: 'GoldieLocsByTina',
        twitter: '@goldielocs',
        youtube: 'GoldieLocsTV'
      },
      // Simplified content structure for modular components
      home: {},
      services: {},
      shop: {},
      consultation: {},
      login: {},
      signup: {},
      cart: {},
      productDetail: {},
      userDashboard: {},
      footer: {}
    }
  }

  // Branding content state - organized by page
  const [brandingContent, setBrandingContent] = useState(getInitialBrandingContent)

  // Save branding content to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('goldielocs_branding_content', JSON.stringify(brandingContent))
  }, [brandingContent])

  // API Loading Functions
  const loadDashboardStats = useCallback(async () => {
    if (loadingRef.current) return

    try {
      setSectionLoading(prev => ({ ...prev, dashboard: true }))
      const response = await adminService.getDashboardStats()
      if (response.success) {
        setDashboardStats(response.data)
      }
    } catch (error) {
      console.error('Error loading dashboard stats:', error)
      setError('Failed to load dashboard statistics')
    } finally {
      setSectionLoading(prev => ({ ...prev, dashboard: false }))
    }
  }, [])

  const loadAppointments = useCallback(async () => {
    if (loadingRef.current) return

    try {
      setSectionLoading(prev => ({ ...prev, appointments: true }))
      const response = await adminService.getAppointments({ page: 1, limit: 20 })
      if (response.success) {
        setAppointments(response.data.appointments || [])
      }
    } catch (error) {
      console.error('Error loading appointments:', error)
      setError('Failed to load appointments')
    } finally {
      setSectionLoading(prev => ({ ...prev, appointments: false }))
    }
  }, [])

  const loadCustomers = useCallback(async () => {
    if (loadingRef.current) return

    try {
      setSectionLoading(prev => ({ ...prev, customers: true }))
      const response = await adminService.getCustomers({ page: 1, limit: 20 })
      if (response.success) {
        setCustomers(response.data.customers || [])
      }
    } catch (error) {
      console.error('Error loading customers:', error)
      setError('Failed to load customers')
    } finally {
      setSectionLoading(prev => ({ ...prev, customers: false }))
    }
  }, [])

  const loadOrders = useCallback(async () => {
    if (loadingRef.current) return

    try {
      setSectionLoading(prev => ({ ...prev, orders: true }))
      const response = await adminService.getOrders({ page: 1, limit: 20 })
      if (response.success) {
        setOrders(response.data.orders || [])
      }
    } catch (error) {
      console.error('Error loading orders:', error)
      setError('Failed to load orders')
    } finally {
      setSectionLoading(prev => ({ ...prev, orders: false }))
    }
  }, [])

  const loadProducts = useCallback(async () => {
    if (loadingRef.current) return

    try {
      setSectionLoading(prev => ({ ...prev, products: true }))
      const response = await adminService.getProducts({ page: 1, limit: 20 })
      if (response.success) {
        setProducts(response.data.products || [])
      }
    } catch (error) {
      console.error('Error loading products:', error)
      setError('Failed to load products')
    } finally {
      setSectionLoading(prev => ({ ...prev, products: false }))
    }
  }, [])

  // Load data based on active tab
  const loadDataForTab = useCallback((tabId) => {
    switch (tabId) {
      case 'overview':
        loadDashboardStats()
        break
      case 'appointments':
        loadAppointments()
        break
      case 'customers':
        loadCustomers()
        break
      case 'orders':
        loadOrders()
        break
      case 'products':
        loadProducts()
        break
      default:
        break
    }
  }, [loadDashboardStats, loadAppointments, loadCustomers, loadOrders, loadProducts])

  // Update URL when tab changes and load required data
  const handleTabChange = (tabId) => {
    setActiveTab(tabId)
    navigateToSubRoute('admin-dashboard', tabId, { scrollToTop: true })
    loadDataForTab(tabId)
  }

  // Initial data loading
  useEffect(() => {
    const initializeData = async () => {
      try {
        setIsLoading(true)
        loadingRef.current = true

        // Simulate loading delay for better UX
        await new Promise(resolve => setTimeout(resolve, 500))

        // Load initial data for overview tab
        await loadDashboardStats()

      } catch (error) {
        console.error('Error initializing dashboard:', error)
        setError('Failed to load dashboard data')
      } finally {
        setIsLoading(false)
        loadingRef.current = false
      }
    }

    initializeData()

    // Handle URL changes
    const handleRouteChange = () => {
      const { mainRoute, subRoute } = parseCurrentRoute()
      if (mainRoute === 'admin-dashboard') {
        // Set initial URL if no sub-route is present
        if (!subRoute) {
          navigateToSubRoute('admin-dashboard', 'overview', { replace: true, scrollToTop: false })
          loadDataForTab('overview')
        } else if (subRoute !== activeTab) {
          // Load data for the current tab
          setActiveTab(subRoute)
          loadDataForTab(subRoute)
        }
      }
    }

    // Listen for route changes
    window.addEventListener('popstate', handleRouteChange)
    handleRouteChange()

    return () => {
      window.removeEventListener('popstate', handleRouteChange)
      loadingRef.current = false
    }
  }, [])

  // Update activeTab when URL changes
  useEffect(() => {
    const { mainRoute, subRoute } = parseCurrentRoute()
    if (mainRoute === 'admin-dashboard') {
      const newTab = subRoute || 'overview'
      if (newTab !== activeTab) {
        setActiveTab(newTab)
        loadDataForTab(newTab)
      }
    }
  }, [window.location.hash])

  // Toast notification helper
  const showToast = (message, type = 'success') => {
    setToast({ message, type })
    setTimeout(() => setToast(null), 3000)
  }

  // Confirmation dialog helper
  const showConfirmDialog = (title, message, onConfirm) => {
    setConfirmDialog({ title, message, onConfirm })
  }

  if (isLoading) {
    return <Loading />
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Dashboard</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: FiBarChart },
    { id: 'appointments', label: 'Appointments', icon: FiCalendar },
    { id: 'customers', label: 'Customers', icon: FiUsers },
    { id: 'orders', label: 'Orders', icon: FiShoppingBag },
    { id: 'products', label: 'Products', icon: FiPackage },
    { id: 'branding', label: 'Branding', icon: FiEdit },
    { id: 'settings', label: 'Settings', icon: FiSettings }
  ]

  // Render the appropriate component based on activeTab
  const renderActiveComponent = () => {
    const commonProps = {
      searchTerm,
      setSearchTerm,
      statusFilter,
      setStatusFilter,
      showAddModal,
      setShowAddModal,
      editingItem,
      setEditingItem,
      viewingItem,
      setViewingItem,
      modalType,
      setModalType,
      showToast,
      showConfirmDialog,
      sectionLoading,
      branding,
      adminData: userProfile
    }

    switch (activeTab) {
      case 'overview':
        return (
          <AdminOverview
            {...commonProps}
            dashboardStats={dashboardStats}
            appointments={appointments.slice(0, 5)}
            customers={customers.slice(0, 5)}
            orders={orders.slice(0, 5)}
            products={products.slice(0, 5)}
            onNavigateToTab={handleTabChange}
          />
        )
      case 'appointments':
        return (
          <AdminAppointments
            {...commonProps}
            appointments={appointments}
            setAppointments={setAppointments}
            loadAppointments={loadAppointments}
          />
        )
      case 'customers':
        return (
          <AdminCustomers
            {...commonProps}
            customers={customers}
            setCustomers={setCustomers}
            loadCustomers={loadCustomers}
          />
        )
      case 'orders':
        return (
          <AdminOrders
            {...commonProps}
            orders={orders}
            setOrders={setOrders}
            loadOrders={loadOrders}
          />
        )
      case 'products':
        return (
          <AdminProducts
            {...commonProps}
            products={products}
            setProducts={setProducts}
            loadProducts={loadProducts}
          />
        )
      case 'branding':
        return (
          <AdminBranding
            {...commonProps}
            brandingContent={brandingContent}
            setBrandingContent={setBrandingContent}
            activeBrandingSection={activeBrandingSection}
            setActiveBrandingSection={setActiveBrandingSection}
          />
        )
      case 'settings':
        return (
          <AdminSettings
            {...commonProps}
          />
        )
      default:
        return (
          <AdminOverview
            {...commonProps}
            dashboardStats={dashboardStats}
            appointments={appointments.slice(0, 5)}
            customers={customers.slice(0, 5)}
            orders={orders.slice(0, 5)}
            products={products.slice(0, 5)}
            onNavigateToTab={handleTabChange}
          />
        )
    }
  }

  return (
    <DashboardLayout
      userType="admin"
      activeTab={activeTab}
      setActiveTab={handleTabChange}
      userData={userProfile}
      onLogout={onLogout}
      sidebarItems={tabs}
    >
      {renderActiveComponent()}
    </DashboardLayout>
  )
}

export default AdminDashboard
